# PDF Processing Pipeline

A comprehensive PDF parsing and processing pipeline built with Python, featuring custom text extraction, header/footer removal, image metadata extraction, and LangChain integration for document processing workflows.

## Features

- **Custom PDF Parsing**: Extract text with layout preservation options
- **Header/Footer Removal**: Automatically detect and remove repeated headers/footers
- **Image Metadata Extraction**: Extract basic image information from PDF pages
- **LangChain Integration**: Convert parsed content to LangChain Documents
- **Text Chunking**: Split large documents into smaller chunks for processing
- **Flexible Output Formats**: Raw data, plain text, or LangChain Documents
- **Interactive Interface**: Easy-to-use command-line interface

## Installation

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

   Or install manually:
   ```bash
   pip install pypdf langchain
   ```

2. **Verify Installation**:
   ```bash
   python test_pipeline.py
   ```

## Usage

### Interactive Interface

Run the main example script for an interactive experience:

```bash
python example.py
```

This will present you with options to:
1. View full parsed raw data
2. Extract full plain text
3. Get LangChain documents (no chunking)
4. Get LangChain documents (with chunking)
5. Show document metadata
6. Show per-page metadata
7. Show cleaned page text (header/footer removed)
8. Show extracted image metadata

### Programmatic Usage

```python
from pipeline import PDFProcessingPipeline

# Initialize pipeline with custom configuration
config = {
    "preserve_layout": False,
    "remove_headers_footers": True,
    "extract_images": True,
    "min_text_length": 20
}
pipeline = PDFProcessingPipeline(config)

# Process PDF and get raw data
raw_data = pipeline.process_single_pdf("path/to/your/file.pdf", output_format="raw")

# Get LangChain documents with chunking
documents = pipeline.process_single_pdf(
    "path/to/your/file.pdf", 
    output_format="langchain", 
    chunk_documents=True,
    chunk_size=500,
    chunk_overlap=50
)

# Get plain text only
text = pipeline.process_single_pdf("path/to/your/file.pdf", output_format="text")
```

## Configuration Options

- **preserve_layout**: Keep layout spacing in text extraction (default: True)
- **remove_headers_footers**: Detect and remove repeated headers/footers (default: True)
- **extract_images**: Extract image metadata from pages (default: False)
- **min_text_length**: Minimum text length for valid pages (default: 10)

## Output Formats

### Raw Format
Returns a dictionary with:
- `full_text`: Combined text from all pages
- `pages`: List of page data with text and metadata
- `document_metadata`: File and PDF metadata
- `total_pages`: Total pages in PDF
- `processed_pages`: Pages kept after filtering
- `total_words`: Total word count

### LangChain Format
Returns a list of LangChain Document objects with:
- `page_content`: Extracted text content
- `metadata`: Combined document and page metadata

### Text Format
Returns a simple string with all extracted text combined.

## File Structure

- `example.py`: Interactive command-line interface
- `pipeline.py`: Main processing pipeline class
- `parser.py`: Custom PDF parser with advanced features
- `langchain_loader.py`: LangChain integration and document loading
- `test_pipeline.py`: Test script to verify installation
- `requirements.txt`: Python dependencies

## Testing

Run the test script to verify everything is working:

```bash
python test_pipeline.py
```

This will test:
- All imports are working correctly
- Pipeline initialization with different configurations
- Parser initialization with custom settings

## Troubleshooting

1. **Import Errors**: Make sure all dependencies are installed with `pip install -r requirements.txt`
2. **PDF Not Found**: Ensure the PDF file path is correct and the file exists
3. **Empty Output**: Check if the PDF has extractable text (some PDFs are image-only)
4. **Memory Issues**: For large PDFs, consider using chunking or processing page by page

## Requirements

- Python 3.7+
- pypdf >= 3.0.0
- langchain >= 0.0.350
- Additional dependencies listed in requirements.txt
