# PDF Processing Pipeline Dependencies

# Core PDF processing
pypdf>=3.0.0

# LangChain for document processing and text splitting
langchain>=0.0.350
langchain-community>=0.0.3
langchain-core>=0.1.0

# Additional dependencies that may be needed
PyYAML>=5.3
SQLAlchemy>=1.4,<3
aiohttp>=3.8.3,<4.0.0
dataclasses-json>=0.5.7,<0.7
jsonpatch>=1.33,<2.0
langsmith>=0.0.63,<0.1.0
numpy>=1,<2
pydantic>=1,<3
requests>=2,<3
tenacity>=8.1.0,<9.0.0
